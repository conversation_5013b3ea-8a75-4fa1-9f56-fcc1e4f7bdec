@echo off
chcp 65001 > nul
echo إنشاء مجلد تجريبي لاختبار البرنامج...
echo =====================================

REM إنشاء مجلد التجربة
mkdir TestProjects 2>nul
cd TestProjects

REM إنشاء مشروع عادي (ليس Git)
mkdir NormalProject
echo This is a normal project > NormalProject\readme.txt

REM إنشاء مشروع Git بدون remote
mkdir GitProjectNoRemote
cd GitProjectNoRemote
git init > nul 2>&1
echo This is a git project without remote > readme.txt
git add . > nul 2>&1
git commit -m "Initial commit" > nul 2>&1
cd ..

echo.
echo تم إنشاء مجلد TestProjects مع مشاريع تجريبية
echo يمكنك الآن تشغيل البرنامج على هذا المجلد:
echo.
echo dotnet run "%cd%"
echo.
pause

@echo off
REM تعيين ترميز UTF-8 لدعم اللغة العربية
chcp 65001 > nul

REM تعيين خط Console لدعم العربية (إذا كان متاحاً)
reg add "HKCU\Console" /v FaceName /t REG_SZ /d "Consolas" /f > nul 2>&1
reg add "HKCU\Console" /v FontFamily /t REG_DWORD /d 54 /f > nul 2>&1
reg add "HKCU\Console" /v FontSize /t REG_DWORD /d 1048592 /f > nul 2>&1

echo.
echo مدير المشاريع - Git Project Manager
echo ====================================
echo.

if "%1"=="" (
    echo الاستخدام: run.bat "مسار_المجلد"
    echo مثال: run.bat "C:\MyProjects"
    echo.
    echo أو يمكنك تشغيل البرنامج بدون معاملات وإدخال المسار يدوياً
    echo.
    pause
    dotnet run
) else (
    dotnet run "%1"
)

echo.
pause

# Git Project Manager

A C# program for managing Git projects and safely deleting fully pushed projects to GitHub.

## Main Features

1. **Project Scanning**: Scans all folders in the specified directory
2. **Git Verification**: Checks if the project is a Git repository connected to GitHub
3. **Push Status Check**: Verifies for unpushed changes (uncommitted or unpushed)
4. **Safe Deletion**: Only deletes fully pushed projects
5. **Comprehensive Report**: Creates a detailed report and saves it in JSON format

## Requirements

- .NET 6.0 or newer
- Git installed on the system
- Windows/Linux/macOS
- UTF-8 support in Terminal/Console

## How to Use

### Method 1 - Direct:
```bash
# Run with path parameter
dotnet run "C:\Path\To\Your\Projects"

# Or run without parameters and enter path manually
dotnet run
```

### Method 2 - Batch File (Windows):
```bash
# Run with path
run.bat "C:\MyProjects"

# Or run without parameters
run.bat
```

### Method 3 - PowerShell:
```powershell
# Run with path
.\run.ps1 -ProjectsPath "C:\MyProjects"

# Or run without parameters
.\run.ps1
```

## Execution Steps

1. The program will scan all folders in the specified path
2. It will check if each folder is a Git repository
3. It will examine Git status for each project:
   - Uncommitted changes
   - Unpushed commits
   - Remote repository existence
4. It will ask if you want to delete fully pushed projects
5. It will create a detailed report and save it as JSON

## Usage Example

```
Git Project Manager
============================================================
Scanning projects in directory: C:\MyProjects
============================================================
Project scanned: Project1 ✓
Project scanned: Project2 ✓
Project scanned: Project3 ✓

Do you want to delete fully pushed projects? (y/n): y

Deleting fully pushed projects...
============================================================
Deleting project: Project1
Project deleted: Project1 ✓
```

## Report

The program creates a comprehensive report including:

- Total number of projects
- Number of Git projects
- Number of fully pushed projects
- Number of deleted projects
- Details for each project (status, errors, etc.)

The report is saved as `project_report.json` in the same projects folder.

## Project States

- **"All changes pushed"**: Project is safe to delete
- **"Has unpushed changes"**: Project contains unpushed changes
- **"No remote repository"**: Project is not connected to GitHub
- **"Not a Git repository"**: Folder is not a Git project

## Safety

- Only deletes fully pushed projects
- Asks for confirmation before deletion
- Creates detailed report before and after operation
- Handles errors safely

## Important Notes

- Make sure Git is installed and in PATH
- Ensure projects are connected to remote repositories
- Review the report before approving deletion
- Keep backups of important projects

## Troubleshooting

If you encounter issues:

1. Make sure Git is installed and added to PATH
2. Verify the folder path is correct
3. Ensure you have read and write permissions
4. Check error messages in the report

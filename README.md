# مدير المشاريع - Git Project Manager

برنامج C# لإدارة مشاريع Git وحذف المشاريع المدفوعة بالكامل إلى GitHub.

## الوظائف الرئيسية

1. **فحص المشاريع**: يقوم بفحص جميع المجلدات في المسار المحدد
2. **التحقق من Git**: يتحقق من كون المشروع مستودع Git مربوط بـ GitHub
3. **فحص حالة Push**: يتحقق من وجود تغييرات غير مدفوعة (uncommitted أو unpushed)
4. **الحذف الآمن**: يحذف فقط المشاريع المدفوعة بالكامل
5. **التقرير الشامل**: ينشئ تقرير مفصل بحالة كل مشروع

## المتطلبات

- .NET 6.0 أو أحدث
- Git مثبت على النظام
- Windows/Linux/macOS
- دعم UTF-8 في Terminal/Console

## دعم اللغة العربية

البرنامج مُحسّن لدعم اللغة العربية بالكامل:

- ✅ **ترميز UTF-8**: دعم كامل للأحرف العربية
- ✅ **اتجاه النص**: عرض النصوص من اليمين إلى اليسار
- ✅ **ألوان مميزة**: ألوان مختلفة لكل نوع من الرسائل
- ✅ **تنسيق جميل**: خطوط فاصلة وتنسيق منظم
- ✅ **رسائل واضحة**: جميع الرسائل باللغة العربية

### إعداد Console للغة العربية (Windows)

لأفضل تجربة على Windows، قم بتشغيل:
```bash
setup_arabic_console.bat
```

هذا سيقوم بإعداد:
- ترميز UTF-8 كافتراضي
- خط Consolas لدعم العربية
- إعدادات Console المحسنة

## كيفية الاستخدام

### 1. تشغيل البرنامج

#### الطريقة الأولى - مباشرة:
```bash
# تشغيل مع تحديد المسار كمعامل
dotnet run "C:\Path\To\Your\Projects"

# أو تشغيل البرنامج وإدخال المسار يدوياً
dotnet run
```

#### الطريقة الثانية - ملف Batch (Windows):
```bash
# تشغيل مع المسار
run.bat "C:\MyProjects"

# أو تشغيل بدون معاملات
run.bat
```

#### الطريقة الثالثة - PowerShell:
```powershell
# تشغيل مع المسار
.\run.ps1 -ProjectsPath "C:\MyProjects"

# أو تشغيل بدون معاملات
.\run.ps1
```

### 2. خطوات التشغيل

1. البرنامج سيفحص جميع المجلدات في المسار المحدد
2. سيتحقق من كون كل مجلد مستودع Git
3. سيفحص حالة Git لكل مشروع:
   - وجود تغييرات غير محفوظة (uncommitted)
   - وجود commits غير مدفوعة (unpushed)
   - وجود remote repository
4. سيسأل عن رغبتك في حذف المشاريع المدفوعة بالكامل
5. سينشئ تقرير مفصل ويحفظه في ملف JSON

### 3. مثال على الاستخدام

```
مدير المشاريع - Git Project Manager
============================================================
أدخل مسار المجلد الذي يحتوي على المشاريع: C:\MyProjects

جاري فحص المشاريع في المجلد: C:\MyProjects
============================================================
تم فحص المشروع: Project1
تم فحص المشروع: Project2
تم فحص المشروع: Project3

هل تريد حذف المشاريع المدفوعة بالكامل؟ (y/n): y

جاري حذف المشاريع المدفوعة بالكامل...
============================================================
جاري حذف المشروع: Project1
تم حذف المشروع: Project1 ✓
```

## التقرير

البرنامج ينشئ تقرير شامل يتضمن:

- إجمالي عدد المشاريع
- عدد مشاريع Git
- عدد المشاريع المدفوعة بالكامل
- عدد المشاريع المحذوفة
- تفاصيل كل مشروع (الحالة، التفاصيل، إلخ)

التقرير يُحفظ في ملف `project_report.json` في نفس مجلد المشاريع.

## حالات المشاريع

- **"جميع التغييرات مدفوعة"**: المشروع آمن للحذف
- **"يوجد تغييرات غير مدفوعة"**: المشروع يحتوي على تغييرات لم تُدفع
- **"لا يوجد remote repository"**: المشروع غير مربوط بـ GitHub
- **"ليس مستودع Git"**: المجلد ليس مشروع Git

## الأمان

- البرنامج يحذف فقط المشاريع المدفوعة بالكامل
- يطلب تأكيد قبل الحذف
- ينشئ تقرير مفصل قبل وبعد العملية
- يتعامل مع الأخطاء بشكل آمن

## ملاحظات مهمة

- تأكد من وجود Git في PATH
- تأكد من أن المشاريع مربوطة بـ remote repositories
- راجع التقرير قبل الموافقة على الحذف
- احتفظ بنسخة احتياطية من المشاريع المهمة

## استكشاف الأخطاء

إذا واجهت مشاكل:

1. تأكد من تثبيت Git وإضافته لـ PATH
2. تأكد من صحة مسار المجلد
3. تأكد من وجود صلاحيات القراءة والكتابة
4. راجع رسائل الخطأ في التقرير

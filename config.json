{"settings": {"defaultProjectsPath": "", "autoDeleteConfirmed": false, "reportFormat": "json", "includeHiddenFolders": false, "gitCommandTimeout": 30, "excludePatterns": ["node_modules", ".vs", "bin", "obj", ".git"]}, "messages": {"arabic": {"scanningProjects": "جاري فحص المشاريع في المجلد", "projectScanned": "تم فحص المشروع", "deletingProjects": "جاري حذف المشاريع المدفوعة بالكامل", "projectDeleted": "تم حذف المشروع", "confirmDelete": "هل تريد حذف المشاريع المدفوعة بالكامل؟ (y/n)", "reportSaved": "تم حفظ التقرير في"}, "english": {"scanningProjects": "Scanning projects in directory", "projectScanned": "Project scanned", "deletingProjects": "Deleting fully pushed projects", "projectDeleted": "Project deleted", "confirmDelete": "Do you want to delete fully pushed projects? (y/n)", "reportSaved": "Report saved to"}}}
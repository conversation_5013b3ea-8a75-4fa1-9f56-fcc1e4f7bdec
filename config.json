{"settings": {"defaultProjectsPath": "", "autoDeleteConfirmed": false, "reportFormat": "json", "includeHiddenFolders": false, "gitCommandTimeout": 30, "language": "english", "useColors": true, "consoleEncoding": "UTF-8", "excludePatterns": ["node_modules", ".vs", "bin", "obj", ".git", "packages", "target", "dist", "build"]}, "messages": {"english": {"scanningProjects": "Scanning projects in directory", "projectScanned": "Project scanned", "deletingProjects": "Deleting fully pushed projects", "projectDeleted": "Project deleted", "confirmDelete": "Do you want to delete fully pushed projects? (y/n)", "reportSaved": "Report saved to", "enterPath": "Enter the path to the directory containing projects", "invalidPath": "Invalid or non-existent path!", "pressAnyKey": "Press any key to exit...", "errorOccurred": "An error occurred"}}}
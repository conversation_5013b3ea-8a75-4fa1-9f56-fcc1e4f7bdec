@echo off
echo إعداد Console لدعم اللغة العربية...
echo ===================================

REM تعيين ترميز UTF-8
chcp 65001 > nul

REM إعداد خط Console لدعم العربية
echo تعيين خط Console...
reg add "HKCU\Console" /v FaceName /t REG_SZ /d "Consolas" /f > nul 2>&1
reg add "HKCU\Console" /v FontFamily /t REG_DWORD /d 54 /f > nul 2>&1
reg add "HKCU\Console" /v FontSize /t REG_DWORD /d 1048592 /f > nul 2>&1

REM إعداد خصائص Console الأخرى
reg add "HKCU\Console" /v CodePage /t REG_DWORD /d 65001 /f > nul 2>&1
reg add "HKCU\Console" /v HistoryBufferSize /t REG_DWORD /d 50 /f > nul 2>&1
reg add "HKCU\Console" /v NumberOfHistoryBuffers /t REG_DWORD /d 4 /f > nul 2>&1
reg add "HKCU\Console" /v QuickEdit /t REG_DWORD /d 1 /f > nul 2>&1

echo.
echo تم إعداد Console بنجاح!
echo يُنصح بإعادة تشغيل Command Prompt للحصول على أفضل النتائج.
echo.

REM اختبار عرض النص العربي
echo اختبار عرض النص العربي:
echo مرحباً بك في مدير المشاريع
echo هذا النص يجب أن يظهر بشكل صحيح
echo.

pause

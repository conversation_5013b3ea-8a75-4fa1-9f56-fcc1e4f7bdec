using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Globalization;
using System.Threading;

namespace ProjectManager
{
    // Windows API functions for UTF-8 Console support
    public static class ConsoleHelper
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetStdHandle(int nStdHandle);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool SetConsoleOutputCP(uint wCodePageID);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool SetConsoleCP(uint wCodePageID);

        public static void SetupConsole()
        {
            try
            {
                if (OperatingSystem.IsWindows())
                {
                    SetConsoleOutputCP(65001); // UTF-8
                    SetConsoleCP(65001); // UTF-8
                }
            }
            catch
            {
                // Ignore errors
            }
        }

        public static void PrintColored(string text, ConsoleColor color = ConsoleColor.White)
        {
            var originalColor = Console.ForegroundColor;
            Console.ForegroundColor = color;

            var lines = text.Split('\n');
            foreach (var line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    Console.WriteLine(line);
                }
                else
                {
                    Console.WriteLine();
                }
            }

            Console.ForegroundColor = originalColor;
        }

        public static void PrintSeparator(char character = '=', int length = 60, ConsoleColor color = ConsoleColor.Yellow)
        {
            PrintColored(new string(character, length), color);
        }
    }
    public class ProjectInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public bool IsGitRepository { get; set; }
        public bool HasUnpushedChanges { get; set; }
        public bool WasDeleted { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new List<string>();
    }

    public class GitProjectManager
    {
        private readonly string _rootDirectory;
        private readonly List<ProjectInfo> _projects = new List<ProjectInfo>();

        public GitProjectManager(string rootDirectory)
        {
            _rootDirectory = rootDirectory;
        }

        public void ScanProjects()
        {
            ConsoleHelper.PrintColored($"Scanning projects in directory: {_rootDirectory}", ConsoleColor.Cyan);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);

            var directories = Directory.GetDirectories(_rootDirectory);

            foreach (var directory in directories)
            {
                var projectInfo = AnalyzeProject(directory);
                _projects.Add(projectInfo);

                ConsoleHelper.PrintColored($"Project scanned: {projectInfo.Name}", ConsoleColor.Green);
            }
        }

        private ProjectInfo AnalyzeProject(string projectPath)
        {
            var projectInfo = new ProjectInfo
            {
                Name = Path.GetFileName(projectPath),
                Path = projectPath,
                IsGitRepository = IsGitRepository(projectPath)
            };

            if (!projectInfo.IsGitRepository)
            {
                projectInfo.Status = "Not a Git repository";
                projectInfo.Details.Add("Project is not connected to Git");
                return projectInfo;
            }

            // Check Git status
            CheckGitStatus(projectInfo);

            return projectInfo;
        }

        private bool IsGitRepository(string path)
        {
            return Directory.Exists(Path.Combine(path, ".git"));
        }

        private void CheckGitStatus(ProjectInfo projectInfo)
        {
            try
            {
                // Check for uncommitted changes
                var statusResult = RunGitCommand(projectInfo.Path, "status --porcelain");
                bool hasUncommittedChanges = !string.IsNullOrWhiteSpace(statusResult.Output);

                // Check for unpushed commits
                var unpushedResult = RunGitCommand(projectInfo.Path, "log @{u}.. --oneline");
                bool hasUnpushedCommits = !string.IsNullOrWhiteSpace(unpushedResult.Output);

                // Check for remote repository
                var remoteResult = RunGitCommand(projectInfo.Path, "remote -v");
                bool hasRemote = !string.IsNullOrWhiteSpace(remoteResult.Output);

                if (!hasRemote)
                {
                    projectInfo.Status = "No remote repository";
                    projectInfo.Details.Add("Project is not connected to GitHub or remote repository");
                    projectInfo.HasUnpushedChanges = true;
                    return;
                }

                if (hasUncommittedChanges)
                {
                    projectInfo.Details.Add("Has uncommitted changes");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (hasUnpushedCommits)
                {
                    projectInfo.Details.Add("Has unpushed commits to remote");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (!hasUncommittedChanges && !hasUnpushedCommits)
                {
                    projectInfo.Status = "All changes pushed";
                    projectInfo.HasUnpushedChanges = false;
                }
                else
                {
                    projectInfo.Status = "Has unpushed changes";
                    projectInfo.HasUnpushedChanges = true;
                }
            }
            catch (Exception ex)
            {
                projectInfo.Status = $"Error checking Git: {ex.Message}";
                projectInfo.HasUnpushedChanges = true;
            }
        }

        private (string Output, string Error, int ExitCode) RunGitCommand(string workingDirectory, string arguments)
        {
            var processInfo = new ProcessStartInfo
            {
                FileName = "git",
                Arguments = arguments,
                WorkingDirectory = workingDirectory,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(processInfo);
            if (process == null) return (string.Empty, "Failed to start process", -1);

            var output = process.StandardOutput.ReadToEnd();
            var error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            return (output, error, process.ExitCode);
        }

        public void DeleteFullyPushedProjects()
        {
            ConsoleHelper.PrintColored("\nDeleting fully pushed projects...", ConsoleColor.Yellow);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);

            var projectsToDelete = _projects.Where(p => p.IsGitRepository && !p.HasUnpushedChanges).ToList();

            foreach (var project in projectsToDelete)
            {
                try
                {
                    ConsoleHelper.PrintColored($"Deleting project: {project.Name}", ConsoleColor.Yellow);
                    Directory.Delete(project.Path, true);
                    project.WasDeleted = true;
                    ConsoleHelper.PrintColored($"Project deleted: {project.Name} ✓", ConsoleColor.Green);
                }
                catch (Exception ex)
                {
                    ConsoleHelper.PrintColored($"Failed to delete project {project.Name}: {ex.Message}", ConsoleColor.Red);
                    project.Details.Add($"Deletion failed: {ex.Message}");
                }
            }
        }

        public void GenerateReport()
        {
            ConsoleHelper.PrintColored("\n", ConsoleColor.White);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Cyan);
            ConsoleHelper.PrintColored("Projects Report", ConsoleColor.Cyan);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Cyan);

            var totalProjects = _projects.Count;
            var gitProjects = _projects.Count(p => p.IsGitRepository);
            var fullyPushedProjects = _projects.Count(p => p.IsGitRepository && !p.HasUnpushedChanges);
            var deletedProjects = _projects.Count(p => p.WasDeleted);

            ConsoleHelper.PrintColored($"Total projects: {totalProjects}", ConsoleColor.White);
            ConsoleHelper.PrintColored($"Git projects: {gitProjects}", ConsoleColor.White);
            ConsoleHelper.PrintColored($"Fully pushed projects: {fullyPushedProjects}", ConsoleColor.Green);
            ConsoleHelper.PrintColored($"Deleted projects: {deletedProjects}", ConsoleColor.Red);
            ConsoleHelper.PrintColored("", ConsoleColor.White);

            // Project details
            foreach (var project in _projects)
            {
                ConsoleHelper.PrintColored($"Project: {project.Name}", ConsoleColor.Yellow);
                ConsoleHelper.PrintColored($"  Path: {project.Path}", ConsoleColor.Gray);
                ConsoleHelper.PrintColored($"  Git Repository: {(project.IsGitRepository ? "Yes" : "No")}",
                    project.IsGitRepository ? ConsoleColor.Green : ConsoleColor.Red);
                ConsoleHelper.PrintColored($"  Status: {project.Status}", ConsoleColor.White);
                ConsoleHelper.PrintColored($"  Deleted: {(project.WasDeleted ? "Yes" : "No")}",
                    project.WasDeleted ? ConsoleColor.Red : ConsoleColor.Green);

                if (project.Details.Any())
                {
                    ConsoleHelper.PrintColored("  Details:", ConsoleColor.Cyan);
                    foreach (var detail in project.Details)
                    {
                        ConsoleHelper.PrintColored($"    - {detail}", ConsoleColor.Gray);
                    }
                }
                ConsoleHelper.PrintColored("", ConsoleColor.White);
            }

            // حفظ التقرير في ملف JSON
            SaveReportToFile();
        }

        private void SaveReportToFile()
        {
            var reportPath = Path.Combine(_rootDirectory, "project_report.json");
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_projects, jsonOptions);
            File.WriteAllText(reportPath, json, Encoding.UTF8);

            ConsoleHelper.PrintColored($"Report saved to: {reportPath}", ConsoleColor.Green);
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            // Setup encoding support
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;

            // Setup console
            ConsoleHelper.SetupConsole();

            PrintWelcomeMessage();

            string rootDirectory;
            
            if (args.Length > 0)
            {
                rootDirectory = args[0];
            }
            else
            {
                ConsoleHelper.PrintColored("Enter the path to the directory containing projects: ", ConsoleColor.Yellow);
                rootDirectory = Console.ReadLine() ?? string.Empty;
            }

            if (string.IsNullOrWhiteSpace(rootDirectory) || !Directory.Exists(rootDirectory))
            {
                ConsoleHelper.PrintColored("Invalid or non-existent path!", ConsoleColor.Red);
                ConsoleHelper.PrintColored("Press any key to exit...", ConsoleColor.Gray);
                Console.ReadKey();
                return;
            }

            var manager = new GitProjectManager(rootDirectory);
            
            try
            {
                // Scan projects
                manager.ScanProjects();

                // Ask about deletion
                ConsoleHelper.PrintColored("\nDo you want to delete fully pushed projects? (y/n): ", ConsoleColor.Yellow);
                var response = Console.ReadLine();

                if (response?.ToLower() == "y" || response?.ToLower() == "yes")
                {
                    manager.DeleteFullyPushedProjects();
                }

                // Generate report
                manager.GenerateReport();
            }
            catch (Exception ex)
            {
                ConsoleHelper.PrintColored($"An error occurred: {ex.Message}", ConsoleColor.Red);
            }

            ConsoleHelper.PrintColored("\nPress any key to exit...", ConsoleColor.Gray);
            Console.ReadKey();
        }

        static void PrintWelcomeMessage()
        {
            ConsoleHelper.PrintColored("Git Project Manager", ConsoleColor.Green);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Globalization;
using System.Threading;

namespace ProjectManager
{
    // دوال Windows API لدعم UTF-8 في Console
    public static class ConsoleHelper
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr GetStdHandle(int nStdHandle);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool SetConsoleOutputCP(uint wCodePageID);

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern bool SetConsoleCP(uint wCodePageID);

        public static void SetupArabicConsole()
        {
            try
            {
                if (OperatingSystem.IsWindows())
                {
                    SetConsoleOutputCP(65001); // UTF-8
                    SetConsoleCP(65001); // UTF-8
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        public static void PrintRightToLeft(string text, ConsoleColor color = ConsoleColor.White)
        {
            var originalColor = Console.ForegroundColor;
            Console.ForegroundColor = color;

            var lines = text.Split('\n');
            foreach (var line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    // استخدام RLE (Right-to-Left Embedding) و PDF (Pop Directional Formatting)
                    // لضمان عرض النص من اليمين إلى اليسار بشكل صحيح
                    Console.WriteLine($"\u202B{line}\u202C");
                }
                else
                {
                    Console.WriteLine();
                }
            }

            Console.ForegroundColor = originalColor;
        }

        public static void PrintSeparator(char character = '=', int length = 60, ConsoleColor color = ConsoleColor.Yellow)
        {
            PrintRightToLeft(new string(character, length), color);
        }

        public static void PrintArabicText(string text, ConsoleColor color = ConsoleColor.White)
        {
            var originalColor = Console.ForegroundColor;
            Console.ForegroundColor = color;

            // تحسين عرض النصوص العربية
            var lines = text.Split('\n');
            foreach (var line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line))
                {
                    // استخدام Unicode Bidirectional Algorithm markers
                    // RLE: Right-to-Left Embedding
                    // PDF: Pop Directional Formatting
                    var formattedLine = $"\u202B{line.Trim()}\u202C";
                    Console.WriteLine(formattedLine);
                }
                else
                {
                    Console.WriteLine();
                }
            }

            Console.ForegroundColor = originalColor;
        }
    }
    public class ProjectInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public bool IsGitRepository { get; set; }
        public bool HasUnpushedChanges { get; set; }
        public bool WasDeleted { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new List<string>();
    }

    public class GitProjectManager
    {
        private readonly string _rootDirectory;
        private readonly List<ProjectInfo> _projects = new List<ProjectInfo>();

        public GitProjectManager(string rootDirectory)
        {
            _rootDirectory = rootDirectory;
        }

        public void ScanProjects()
        {
            ConsoleHelper.PrintArabicText($"جاري فحص المشاريع في المجلد: {_rootDirectory}", ConsoleColor.Cyan);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);

            var directories = Directory.GetDirectories(_rootDirectory);

            foreach (var directory in directories)
            {
                var projectInfo = AnalyzeProject(directory);
                _projects.Add(projectInfo);

                ConsoleHelper.PrintArabicText($"تم فحص المشروع: {projectInfo.Name}", ConsoleColor.Green);
            }
        }

        private ProjectInfo AnalyzeProject(string projectPath)
        {
            var projectInfo = new ProjectInfo
            {
                Name = Path.GetFileName(projectPath),
                Path = projectPath,
                IsGitRepository = IsGitRepository(projectPath)
            };

            if (!projectInfo.IsGitRepository)
            {
                projectInfo.Status = "ليس مستودع Git";
                projectInfo.Details.Add("المشروع غير مربوط بـ Git");
                return projectInfo;
            }

            // التحقق من حالة Git
            CheckGitStatus(projectInfo);
            
            return projectInfo;
        }

        private bool IsGitRepository(string path)
        {
            return Directory.Exists(Path.Combine(path, ".git"));
        }

        private void CheckGitStatus(ProjectInfo projectInfo)
        {
            try
            {
                // التحقق من وجود تغييرات غير محفوظة
                var statusResult = RunGitCommand(projectInfo.Path, "status --porcelain");
                bool hasUncommittedChanges = !string.IsNullOrWhiteSpace(statusResult.Output);

                // التحقق من وجود commits غير مدفوعة
                var unpushedResult = RunGitCommand(projectInfo.Path, "log @{u}.. --oneline");
                bool hasUnpushedCommits = !string.IsNullOrWhiteSpace(unpushedResult.Output);

                // التحقق من وجود remote
                var remoteResult = RunGitCommand(projectInfo.Path, "remote -v");
                bool hasRemote = !string.IsNullOrWhiteSpace(remoteResult.Output);

                if (!hasRemote)
                {
                    projectInfo.Status = "لا يوجد remote repository";
                    projectInfo.Details.Add("المشروع غير مربوط بـ GitHub أو remote repository");
                    projectInfo.HasUnpushedChanges = true;
                    return;
                }

                if (hasUncommittedChanges)
                {
                    projectInfo.Details.Add("يوجد تغييرات غير محفوظة (uncommitted changes)");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (hasUnpushedCommits)
                {
                    projectInfo.Details.Add("يوجد commits غير مدفوعة للـ remote");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (!hasUncommittedChanges && !hasUnpushedCommits)
                {
                    projectInfo.Status = "جميع التغييرات مدفوعة";
                    projectInfo.HasUnpushedChanges = false;
                }
                else
                {
                    projectInfo.Status = "يوجد تغييرات غير مدفوعة";
                    projectInfo.HasUnpushedChanges = true;
                }
            }
            catch (Exception ex)
            {
                projectInfo.Status = $"خطأ في فحص Git: {ex.Message}";
                projectInfo.HasUnpushedChanges = true;
            }
        }

        private (string Output, string Error, int ExitCode) RunGitCommand(string workingDirectory, string arguments)
        {
            var processInfo = new ProcessStartInfo
            {
                FileName = "git",
                Arguments = arguments,
                WorkingDirectory = workingDirectory,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(processInfo);
            if (process == null) return (string.Empty, "Failed to start process", -1);

            var output = process.StandardOutput.ReadToEnd();
            var error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            return (output, error, process.ExitCode);
        }

        public void DeleteFullyPushedProjects()
        {
            ConsoleHelper.PrintArabicText("\nجاري حذف المشاريع المدفوعة بالكامل...", ConsoleColor.Yellow);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);

            var projectsToDelete = _projects.Where(p => p.IsGitRepository && !p.HasUnpushedChanges).ToList();

            foreach (var project in projectsToDelete)
            {
                try
                {
                    ConsoleHelper.PrintArabicText($"جاري حذف المشروع: {project.Name}", ConsoleColor.Yellow);
                    Directory.Delete(project.Path, true);
                    project.WasDeleted = true;
                    ConsoleHelper.PrintArabicText($"تم حذف المشروع: {project.Name} ✓", ConsoleColor.Green);
                }
                catch (Exception ex)
                {
                    ConsoleHelper.PrintArabicText($"فشل في حذف المشروع {project.Name}: {ex.Message}", ConsoleColor.Red);
                    project.Details.Add($"فشل الحذف: {ex.Message}");
                }
            }
        }

        public void GenerateReport()
        {
            ConsoleHelper.PrintArabicText("\n", ConsoleColor.White);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Cyan);
            ConsoleHelper.PrintArabicText("تقرير المشاريع", ConsoleColor.Cyan);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Cyan);

            var totalProjects = _projects.Count;
            var gitProjects = _projects.Count(p => p.IsGitRepository);
            var fullyPushedProjects = _projects.Count(p => p.IsGitRepository && !p.HasUnpushedChanges);
            var deletedProjects = _projects.Count(p => p.WasDeleted);

            ConsoleHelper.PrintArabicText($"إجمالي المشاريع: {totalProjects}", ConsoleColor.White);
            ConsoleHelper.PrintArabicText($"مشاريع Git: {gitProjects}", ConsoleColor.White);
            ConsoleHelper.PrintArabicText($"مشاريع مدفوعة بالكامل: {fullyPushedProjects}", ConsoleColor.Green);
            ConsoleHelper.PrintArabicText($"مشاريع تم حذفها: {deletedProjects}", ConsoleColor.Red);
            ConsoleHelper.PrintArabicText("", ConsoleColor.White);

            // تفاصيل كل مشروع
            foreach (var project in _projects)
            {
                ConsoleHelper.PrintArabicText($"المشروع: {project.Name}", ConsoleColor.Yellow);
                ConsoleHelper.PrintArabicText($"  المسار: {project.Path}", ConsoleColor.Gray);
                ConsoleHelper.PrintArabicText($"  مستودع Git: {(project.IsGitRepository ? "نعم" : "لا")}",
                    project.IsGitRepository ? ConsoleColor.Green : ConsoleColor.Red);
                ConsoleHelper.PrintArabicText($"  الحالة: {project.Status}", ConsoleColor.White);
                ConsoleHelper.PrintArabicText($"  تم الحذف: {(project.WasDeleted ? "نعم" : "لا")}",
                    project.WasDeleted ? ConsoleColor.Red : ConsoleColor.Green);

                if (project.Details.Any())
                {
                    ConsoleHelper.PrintArabicText("  التفاصيل:", ConsoleColor.Cyan);
                    foreach (var detail in project.Details)
                    {
                        ConsoleHelper.PrintArabicText($"    - {detail}", ConsoleColor.Gray);
                    }
                }
                ConsoleHelper.PrintArabicText("", ConsoleColor.White);
            }

            // حفظ التقرير في ملف JSON
            SaveReportToFile();
        }

        private void SaveReportToFile()
        {
            var reportPath = Path.Combine(_rootDirectory, "project_report.json");
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_projects, jsonOptions);
            File.WriteAllText(reportPath, json, Encoding.UTF8);

            ConsoleHelper.PrintArabicText($"تم حفظ التقرير في: {reportPath}", ConsoleColor.Green);
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            // إعداد دعم اللغة العربية والترميز
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;

            // تعيين الثقافة العربية
            Thread.CurrentThread.CurrentCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("ar-SA");

            // إعداد Console للغة العربية
            ConsoleHelper.SetupArabicConsole();

            PrintWelcomeMessage();

            string rootDirectory;
            
            if (args.Length > 0)
            {
                rootDirectory = args[0];
            }
            else
            {
                ConsoleHelper.PrintArabicText("أدخل مسار المجلد الذي يحتوي على المشاريع: ", ConsoleColor.Yellow);
                Console.Write("\u202B"); // Right-to-Left Embedding
                rootDirectory = Console.ReadLine() ?? string.Empty;
            }

            if (string.IsNullOrWhiteSpace(rootDirectory) || !Directory.Exists(rootDirectory))
            {
                ConsoleHelper.PrintArabicText("المسار غير صحيح أو غير موجود!", ConsoleColor.Red);
                ConsoleHelper.PrintArabicText("اضغط أي مفتاح للخروج...", ConsoleColor.Gray);
                Console.ReadKey();
                return;
            }

            var manager = new GitProjectManager(rootDirectory);
            
            try
            {
                // فحص المشاريع
                manager.ScanProjects();
                
                // السؤال عن الحذف
                ConsoleHelper.PrintArabicText("\nهل تريد حذف المشاريع المدفوعة بالكامل؟ (y/n): ", ConsoleColor.Yellow);
                Console.Write("\u202B"); // Right-to-Left Embedding
                var response = Console.ReadLine();

                if (response?.ToLower() == "y" || response?.ToLower() == "yes" || response?.ToLower() == "نعم")
                {
                    manager.DeleteFullyPushedProjects();
                }
                
                // إنشاء التقرير
                manager.GenerateReport();
            }
            catch (Exception ex)
            {
                ConsoleHelper.PrintArabicText($"حدث خطأ: {ex.Message}", ConsoleColor.Red);
            }

            ConsoleHelper.PrintArabicText("\nاضغط أي مفتاح للخروج...", ConsoleColor.Gray);
            Console.ReadKey();
        }

        static void PrintWelcomeMessage()
        {
            ConsoleHelper.PrintArabicText("مدير المشاريع - Git Project Manager", ConsoleColor.Green);
            ConsoleHelper.PrintSeparator('=', 60, ConsoleColor.Yellow);
        }
    }
}

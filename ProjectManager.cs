using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;

namespace ProjectManager
{
    public class ProjectInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public bool IsGitRepository { get; set; }
        public bool HasUnpushedChanges { get; set; }
        public bool WasDeleted { get; set; }
        public string Status { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new List<string>();
    }

    public class GitProjectManager
    {
        private readonly string _rootDirectory;
        private readonly List<ProjectInfo> _projects = new List<ProjectInfo>();

        public GitProjectManager(string rootDirectory)
        {
            _rootDirectory = rootDirectory;
        }

        public void ScanProjects()
        {
            Console.WriteLine($"جاري فحص المشاريع في المجلد: {_rootDirectory}");
            Console.WriteLine(new string('=', 60));

            var directories = Directory.GetDirectories(_rootDirectory);
            
            foreach (var directory in directories)
            {
                var projectInfo = AnalyzeProject(directory);
                _projects.Add(projectInfo);
                
                Console.WriteLine($"تم فحص المشروع: {projectInfo.Name}");
            }
        }

        private ProjectInfo AnalyzeProject(string projectPath)
        {
            var projectInfo = new ProjectInfo
            {
                Name = Path.GetFileName(projectPath),
                Path = projectPath,
                IsGitRepository = IsGitRepository(projectPath)
            };

            if (!projectInfo.IsGitRepository)
            {
                projectInfo.Status = "ليس مستودع Git";
                projectInfo.Details.Add("المشروع غير مربوط بـ Git");
                return projectInfo;
            }

            // التحقق من حالة Git
            CheckGitStatus(projectInfo);
            
            return projectInfo;
        }

        private bool IsGitRepository(string path)
        {
            return Directory.Exists(Path.Combine(path, ".git"));
        }

        private void CheckGitStatus(ProjectInfo projectInfo)
        {
            try
            {
                // التحقق من وجود تغييرات غير محفوظة
                var statusResult = RunGitCommand(projectInfo.Path, "status --porcelain");
                bool hasUncommittedChanges = !string.IsNullOrWhiteSpace(statusResult.Output);

                // التحقق من وجود commits غير مدفوعة
                var unpushedResult = RunGitCommand(projectInfo.Path, "log @{u}.. --oneline");
                bool hasUnpushedCommits = !string.IsNullOrWhiteSpace(unpushedResult.Output);

                // التحقق من وجود remote
                var remoteResult = RunGitCommand(projectInfo.Path, "remote -v");
                bool hasRemote = !string.IsNullOrWhiteSpace(remoteResult.Output);

                if (!hasRemote)
                {
                    projectInfo.Status = "لا يوجد remote repository";
                    projectInfo.Details.Add("المشروع غير مربوط بـ GitHub أو remote repository");
                    projectInfo.HasUnpushedChanges = true;
                    return;
                }

                if (hasUncommittedChanges)
                {
                    projectInfo.Details.Add("يوجد تغييرات غير محفوظة (uncommitted changes)");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (hasUnpushedCommits)
                {
                    projectInfo.Details.Add("يوجد commits غير مدفوعة للـ remote");
                    projectInfo.HasUnpushedChanges = true;
                }

                if (!hasUncommittedChanges && !hasUnpushedCommits)
                {
                    projectInfo.Status = "جميع التغييرات مدفوعة";
                    projectInfo.HasUnpushedChanges = false;
                }
                else
                {
                    projectInfo.Status = "يوجد تغييرات غير مدفوعة";
                    projectInfo.HasUnpushedChanges = true;
                }
            }
            catch (Exception ex)
            {
                projectInfo.Status = $"خطأ في فحص Git: {ex.Message}";
                projectInfo.HasUnpushedChanges = true;
            }
        }

        private (string Output, string Error, int ExitCode) RunGitCommand(string workingDirectory, string arguments)
        {
            var processInfo = new ProcessStartInfo
            {
                FileName = "git",
                Arguments = arguments,
                WorkingDirectory = workingDirectory,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(processInfo);
            if (process == null) return (string.Empty, "Failed to start process", -1);

            var output = process.StandardOutput.ReadToEnd();
            var error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            return (output, error, process.ExitCode);
        }

        public void DeleteFullyPushedProjects()
        {
            Console.WriteLine("\nجاري حذف المشاريع المدفوعة بالكامل...");
            Console.WriteLine(new string('=', 60));

            var projectsToDelete = _projects.Where(p => p.IsGitRepository && !p.HasUnpushedChanges).ToList();

            foreach (var project in projectsToDelete)
            {
                try
                {
                    Console.WriteLine($"جاري حذف المشروع: {project.Name}");
                    Directory.Delete(project.Path, true);
                    project.WasDeleted = true;
                    Console.WriteLine($"تم حذف المشروع: {project.Name} ✓");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"فشل في حذف المشروع {project.Name}: {ex.Message}");
                    project.Details.Add($"فشل الحذف: {ex.Message}");
                }
            }
        }

        public void GenerateReport()
        {
            Console.WriteLine("\n" + new string('=', 60));
            Console.WriteLine("تقرير المشاريع");
            Console.WriteLine(new string('=', 60));

            var totalProjects = _projects.Count;
            var gitProjects = _projects.Count(p => p.IsGitRepository);
            var fullyPushedProjects = _projects.Count(p => p.IsGitRepository && !p.HasUnpushedChanges);
            var deletedProjects = _projects.Count(p => p.WasDeleted);

            Console.WriteLine($"إجمالي المشاريع: {totalProjects}");
            Console.WriteLine($"مشاريع Git: {gitProjects}");
            Console.WriteLine($"مشاريع مدفوعة بالكامل: {fullyPushedProjects}");
            Console.WriteLine($"مشاريع تم حذفها: {deletedProjects}");
            Console.WriteLine();

            // تفاصيل كل مشروع
            foreach (var project in _projects)
            {
                Console.WriteLine($"المشروع: {project.Name}");
                Console.WriteLine($"  المسار: {project.Path}");
                Console.WriteLine($"  مستودع Git: {(project.IsGitRepository ? "نعم" : "لا")}");
                Console.WriteLine($"  الحالة: {project.Status}");
                Console.WriteLine($"  تم الحذف: {(project.WasDeleted ? "نعم" : "لا")}");
                
                if (project.Details.Any())
                {
                    Console.WriteLine("  التفاصيل:");
                    foreach (var detail in project.Details)
                    {
                        Console.WriteLine($"    - {detail}");
                    }
                }
                Console.WriteLine();
            }

            // حفظ التقرير في ملف JSON
            SaveReportToFile();
        }

        private void SaveReportToFile()
        {
            var reportPath = Path.Combine(_rootDirectory, "project_report.json");
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };

            var json = JsonSerializer.Serialize(_projects, jsonOptions);
            File.WriteAllText(reportPath, json, Encoding.UTF8);
            
            Console.WriteLine($"تم حفظ التقرير في: {reportPath}");
        }
    }

    class Program
    {
        static void Main(string[] args)
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.WriteLine("مدير المشاريع - Git Project Manager");
            Console.WriteLine(new string('=', 60));

            string rootDirectory;
            
            if (args.Length > 0)
            {
                rootDirectory = args[0];
            }
            else
            {
                Console.Write("أدخل مسار المجلد الذي يحتوي على المشاريع: ");
                rootDirectory = Console.ReadLine() ?? string.Empty;
            }

            if (string.IsNullOrWhiteSpace(rootDirectory) || !Directory.Exists(rootDirectory))
            {
                Console.WriteLine("المسار غير صحيح أو غير موجود!");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
                return;
            }

            var manager = new GitProjectManager(rootDirectory);
            
            try
            {
                // فحص المشاريع
                manager.ScanProjects();
                
                // السؤال عن الحذف
                Console.WriteLine("\nهل تريد حذف المشاريع المدفوعة بالكامل؟ (y/n): ");
                var response = Console.ReadLine();
                
                if (response?.ToLower() == "y" || response?.ToLower() == "yes")
                {
                    manager.DeleteFullyPushedProjects();
                }
                
                // إنشاء التقرير
                manager.GenerateReport();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"حدث خطأ: {ex.Message}");
            }

            Console.WriteLine("\nاضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}

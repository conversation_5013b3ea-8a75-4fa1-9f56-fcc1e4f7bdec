﻿[
  {
    "Name": "GitProject",
    "Path": "TestProjects\\GitProject",
    "IsGitRepository": true,
    "HasUnpushedChanges": true,
    "WasDeleted": false,
    "Status": "Error checking Git: An error occurred trying to start process 'git' with working directory 'TestProjects\\GitProject'. The system cannot find the file specified.",
    "Details": []
  },
  {
    "Name": "NormalProject",
    "Path": "TestProjects\\NormalProject",
    "IsGitRepository": false,
    "HasUnpushedChanges": false,
    "WasDeleted": false,
    "Status": "Not a Git repository",
    "Details": [
      "Project is not connected to Git"
    ]
  }
]
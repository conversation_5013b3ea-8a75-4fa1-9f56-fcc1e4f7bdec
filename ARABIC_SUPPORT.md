# دعم اللغة العربية في مدير المشاريع

## 🌟 التحسينات المضافة

تم تحسين البرنامج لدعم اللغة العربية بالكامل مع التحسينات التالية:

### 1. ✅ دعم الترميز UTF-8
- تعيين `Console.OutputEncoding = Encoding.UTF8`
- تعيين `Console.InputEncoding = Encoding.UTF8`
- دعم كامل للأحرف العربية

### 2. ✅ دعم الثقافة العربية
- تعيين `CurrentCulture = "ar-SA"`
- تعيين `CurrentUICulture = "ar-SA"`
- تنسيق التواريخ والأرقام بالطريقة العربية

### 3. ✅ اتجاه النص من اليمين إلى اليسار
- استخدام `Right-to-Left Mark (U+200F)`
- دالة `PrintRightToLeft()` مخصصة
- عرض النصوص بالاتجاه الصحيح

### 4. ✅ نظام ألوان متقدم
- ألوان مختلفة لكل نوع من الرسائل:
  - 🟢 **أخضر**: رسائل النجاح والإكمال
  - 🔴 **أحمر**: رسائل الخطأ والتحذير
  - 🟡 **أصفر**: رسائل المعلومات والانتظار
  - 🔵 **أزرق**: العناوين والتقارير
  - ⚪ **أبيض**: النصوص العادية
  - 🔘 **رمادي**: التفاصيل الإضافية

### 5. ✅ تنسيق جميل ومنظم
- خطوط فاصلة ملونة
- مسافات وتنسيق منظم
- رسائل واضحة ومفهومة

## 🛠️ الدوال المضافة

### `ConsoleHelper` Class
```csharp
public static class ConsoleHelper
{
    // إعداد Console للغة العربية
    public static void SetupArabicConsole()
    
    // طباعة نص من اليمين إلى اليسار مع لون
    public static void PrintRightToLeft(string text, ConsoleColor color)
    
    // طباعة خط فاصل ملون
    public static void PrintSeparator(char character, int length, ConsoleColor color)
}
```

### Windows API Integration
- `SetConsoleOutputCP(65001)` - تعيين UTF-8
- `SetConsoleCP(65001)` - تعيين UTF-8 للإدخال
- دعم Windows Console بالكامل

## 📁 ملفات الإعداد

### 1. `setup_arabic_console.bat`
- إعداد Console في Windows
- تعيين خط Consolas
- تفعيل UTF-8

### 2. `Setup-ArabicConsole.ps1`
- إعداد PowerShell للغة العربية
- إعدادات دائمة في Profile
- اختبار العرض

### 3. `run.bat` المحسن
- دعم UTF-8 تلقائياً
- إعداد خط Console
- تشغيل محسن

### 4. `run.ps1` المحسن
- دعم معاملات PowerShell
- رسائل عربية
- تشغيل مرن

## 🎯 أمثلة على الرسائل

### قبل التحسين:
```
Scanning projects in directory: C:\Projects
Project scanned: MyProject
Do you want to delete fully pushed projects? (y/n):
```

### بعد التحسين:
```
جاري فحص المشاريع في المجلد: C:\Projects
============================================================
تم فحص المشروع: MyProject ✓

هل تريد حذف المشاريع المدفوعة بالكامل؟ (y/n): 
```

## 🚀 كيفية الاستخدام

### الطريقة الأولى - إعداد سريع:
```bash
# تشغيل إعداد Console
setup_arabic_console.bat

# تشغيل البرنامج
dotnet run "C:\MyProjects"
```

### الطريقة الثانية - PowerShell:
```powershell
# إعداد PowerShell للعربية
.\Setup-ArabicConsole.ps1 -Permanent

# تشغيل البرنامج
.\run.ps1 -ProjectsPath "C:\MyProjects"
```

### الطريقة الثالثة - مباشرة:
```bash
# تشغيل مباشر (مع إعداد تلقائي)
run.bat "C:\MyProjects"
```

## 🔧 استكشاف الأخطاء

### إذا لم تظهر النصوص العربية بشكل صحيح:

1. **تأكد من ترميز UTF-8**:
   ```bash
   chcp 65001
   ```

2. **تغيير خط Console**:
   - انقر بزر الماوس الأيمن على شريط العنوان
   - اختر Properties
   - غيّر الخط إلى Consolas أو Courier New

3. **تشغيل إعداد Console**:
   ```bash
   setup_arabic_console.bat
   ```

### إذا كانت النصوص معكوسة:
- البرنامج يستخدم `Right-to-Left Mark` تلقائياً
- تأكد من دعم Terminal للـ RTL

### إذا لم تظهر الألوان:
- تأكد من دعم Terminal للألوان
- استخدم Windows Terminal أو PowerShell الحديث

## 📊 مثال على التقرير النهائي

```
============================================================
تقرير المشاريع
============================================================
إجمالي المشاريع: 5
مشاريع Git: 4
مشاريع مدفوعة بالكامل: 2
مشاريع تم حذفها: 2

المشروع: MyWebApp
  المسار: C:\Projects\MyWebApp
  مستودع Git: نعم
  الحالة: جميع التغييرات مدفوعة
  تم الحذف: نعم

المشروع: TestProject
  المسار: C:\Projects\TestProject
  مستودع Git: نعم
  الحالة: يوجد تغييرات غير مدفوعة
  تم الحذف: لا
  التفاصيل:
    - يوجد تغييرات غير محفوظة (uncommitted changes)
    - يوجد commits غير مدفوعة للـ remote

تم حفظ التقرير في: C:\Projects\project_report.json
```

## ✨ الخلاصة

البرنامج الآن يدعم اللغة العربية بالكامل مع:
- ✅ عرض صحيح من اليمين إلى اليسار
- ✅ ألوان مميزة لكل نوع من الرسائل
- ✅ تنسيق جميل ومنظم
- ✅ دعم كامل للترميز العربي
- ✅ إعدادات تلقائية للـ Console
- ✅ ملفات مساعدة للإعداد
- ✅ توافق مع Windows/PowerShell/CMD

# Git Project Manager
# PowerShell Script

param(
    [string]$ProjectsPath = ""
)

Write-Host "Git Project Manager" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host ""

if ($ProjectsPath -eq "") {
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host ".\run.ps1 -ProjectsPath 'C:\MyProjects'" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Or you can run the program without parameters and enter the path manually" -ForegroundColor Yellow
    Write-Host ""
    
    dotnet run
} else {
    dotnet run $ProjectsPath
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

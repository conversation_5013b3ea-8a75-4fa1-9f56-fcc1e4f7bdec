# مدير المشاريع - Git Project Manager
# PowerShell Script

param(
    [string]$ProjectsPath = ""
)

Write-Host "مدير المشاريع - Git Project Manager" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

if ($ProjectsPath -eq "") {
    Write-Host "الاستخدام:" -ForegroundColor Yellow
    Write-Host ".\run.ps1 -ProjectsPath 'C:\MyProjects'" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "أو يمكنك تشغيل البرنامج بدون معاملات وإدخال المسار يدوياً" -ForegroundColor Yellow
    Write-Host ""
    
    dotnet run
} else {
    dotnet run $ProjectsPath
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

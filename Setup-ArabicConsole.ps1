# إعداد PowerShell Console لدعم اللغة العربية
# Setup PowerShell Console for Arabic Language Support

param(
    [switch]$Permanent = $false
)

Write-Host "إعداد Console لدعم اللغة العربية..." -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""

# تعيين ترميز UTF-8
Write-Host "تعيين ترميز UTF-8..." -ForegroundColor Yellow
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# تعيين الثقافة العربية
Write-Host "تعيين الثقافة العربية..." -ForegroundColor Yellow
[System.Threading.Thread]::CurrentThread.CurrentCulture = [System.Globalization.CultureInfo]::new("ar-SA")
[System.Threading.Thread]::CurrentThread.CurrentUICulture = [System.Globalization.CultureInfo]::new("ar-SA")

# إعداد خط Console (Windows فقط)
if ($IsWindows -or $env:OS -eq "Windows_NT") {
    Write-Host "إعداد خط Console..." -ForegroundColor Yellow
    
    try {
        # تعيين خط Consolas
        $regPath = "HKCU:\Console"
        if (!(Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "FaceName" -Value "Consolas" -Type String
        Set-ItemProperty -Path $regPath -Name "FontFamily" -Value 54 -Type DWord
        Set-ItemProperty -Path $regPath -Name "FontSize" -Value 1048592 -Type DWord
        Set-ItemProperty -Path $regPath -Name "CodePage" -Value 65001 -Type DWord
        
        Write-Host "تم إعداد خط Console بنجاح!" -ForegroundColor Green
    }
    catch {
        Write-Host "تعذر إعداد خط Console: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# إعداد PowerShell Profile (إذا طُلب ذلك)
if ($Permanent) {
    Write-Host "إضافة الإعدادات إلى PowerShell Profile..." -ForegroundColor Yellow
    
    $profileContent = @"
# إعدادات اللغة العربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8
[System.Threading.Thread]::CurrentThread.CurrentCulture = [System.Globalization.CultureInfo]::new("ar-SA")
[System.Threading.Thread]::CurrentThread.CurrentUICulture = [System.Globalization.CultureInfo]::new("ar-SA")
"@

    if (!(Test-Path $PROFILE)) {
        New-Item -Path $PROFILE -ItemType File -Force | Out-Null
    }
    
    Add-Content -Path $PROFILE -Value $profileContent
    Write-Host "تم إضافة الإعدادات إلى PowerShell Profile!" -ForegroundColor Green
}

Write-Host ""
Write-Host "تم إعداد Console بنجاح!" -ForegroundColor Green
Write-Host ""

# اختبار عرض النص العربي
Write-Host "اختبار عرض النص العربي:" -ForegroundColor Cyan
Write-Host "مرحباً بك في مدير المشاريع" -ForegroundColor White
Write-Host "هذا النص يجب أن يظهر بشكل صحيح" -ForegroundColor White
Write-Host "البرنامج جاهز للاستخدام!" -ForegroundColor Green
Write-Host ""

if ($Permanent) {
    Write-Host "ملاحظة: تم حفظ الإعدادات بشكل دائم في PowerShell Profile" -ForegroundColor Yellow
    Write-Host "ستكون الإعدادات فعالة في جلسات PowerShell المستقبلية" -ForegroundColor Yellow
} else {
    Write-Host "ملاحظة: هذه الإعدادات مؤقتة لهذه الجلسة فقط" -ForegroundColor Yellow
    Write-Host "لجعل الإعدادات دائمة، استخدم: .\Setup-ArabicConsole.ps1 -Permanent" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "يمكنك الآن تشغيل البرنامج:" -ForegroundColor Cyan
Write-Host "dotnet run" -ForegroundColor White
Write-Host ""
